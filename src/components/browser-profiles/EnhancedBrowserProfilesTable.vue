<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  Chrome, 
  Badge as Edge, 
  Globe, 
  MoreVertical, 
  Search, 
  Filter, 
  Columns, 
  Download, 
  Trash2, 
  PlusCircle, 
  Edit, 
  Play, 
  Tag 
} from 'lucide-vue-next'
import type { BrowserProfile } from '@/types/BrowserProfile'

// Props
const props = defineProps<{
  profiles?: BrowserProfile[]
}>()

// Reactive state
const searchTerm = ref('')
const selectedProfiles = ref<string[]>([])

// Mock data for demonstration
const mockProfiles: BrowserProfile[] = [
  {
    id: '1',
    name: 'Shopping Profile',
    targetWebsites: ['Amazon', 'eBay'],
    fingerprintBrowserType: 'Chrome',
    version: '137.137',
    proxyEnabled: true,
    created: '2025-05-10T12:00:00Z',
    lastUsed: '2025-05-15T14:30:00Z',
    status: 'active',
    tags: ['shopping', 'personal']
  },
  {
    id: '2',
    name: 'Social Media',
    targetWebsites: ['Facebook', 'Twitter'],
    fingerprintBrowserType: 'Edge',
    version: '137.137',
    proxyEnabled: true,
    created: '2025-04-20T10:15:00Z',
    lastUsed: '2025-05-17T09:45:00Z',
    status: 'active',
    tags: ['social', 'work']
  },
  {
    id: '3',
    name: 'Research Profile',
    targetWebsites: ['Google', 'Scholar'],
    fingerprintBrowserType: 'Chrome',
    version: '137.137',
    proxyEnabled: false,
    created: '2025-05-01T15:30:00Z',
    lastUsed: '2025-05-16T11:20:00Z',
    status: 'inactive',
    tags: ['research', 'work']
  },
  {
    id: '4',
    name: 'Secure Transactions',
    targetWebsites: ['PayPal', 'Stripe'],
    fingerprintBrowserType: 'Opera',
    version: '137.137',
    proxyEnabled: true,
    created: '2025-03-15T08:45:00Z',
    lastUsed: '2025-05-10T16:30:00Z',
    status: 'error',
    tags: ['finance', 'personal', 'secure']
  }
]

// Helper functions
const getBrowserIcon = (type: string) => {
  switch (type) {
    case 'Chrome':
      return Chrome
    case 'Edge':
      return Edge
    default:
      return Globe
  }
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'active':
      return {
        text: 'Active',
        classes: 'px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
      }
    case 'inactive':
      return {
        text: 'Inactive',
        classes: 'px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
      }
    case 'error':
      return {
        text: 'Error',
        classes: 'px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
      }
    default:
      return {
        text: status,
        classes: 'px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
      }
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const toggleSelectProfile = (id: string) => {
  const index = selectedProfiles.value.indexOf(id)
  if (index > -1) {
    selectedProfiles.value.splice(index, 1)
  } else {
    selectedProfiles.value.push(id)
  }
}

const toggleSelectAll = () => {
  if (selectedProfiles.value.length === allProfiles.value.length) {
    selectedProfiles.value = []
  } else {
    selectedProfiles.value = allProfiles.value.map(profile => profile.id)
  }
}

// Computed properties
const allProfiles = computed(() => props.profiles || mockProfiles)

const filteredProfiles = computed(() => {
  return allProfiles.value.filter(profile =>
    profile.name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
    profile.fingerprintBrowserType.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
    profile.targetWebsites.some(site => site.toLowerCase().includes(searchTerm.value.toLowerCase())) ||
    profile.tags.some(tag => tag.toLowerCase().includes(searchTerm.value.toLowerCase()))
  )
})
</script>

<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <!-- Header with search and actions -->
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex flex-col md:flex-row space-y-3 md:space-y-0 md:items-center md:justify-between">
      <!-- Search -->
      <div class="w-full md:w-1/3 relative">
        <input
          v-model="searchTerm"
          type="text"
          placeholder="Search profiles..."
          class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
        />
        <div class="absolute left-3 top-2.5 text-gray-400">
          <Search :size="18" />
        </div>
      </div>
      
      <!-- Action buttons -->
      <div class="flex flex-wrap items-center space-x-2">
        <button class="px-3 py-2 rounded-md bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 flex items-center text-sm">
          <Filter :size="16" class="mr-1" />
          <span class="text-gray-700 dark:text-gray-300">Filters</span>
        </button>

        <button class="px-3 py-2 rounded-md bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 flex items-center text-sm">
          <Columns :size="16" class="mr-1" />
          <span class="text-gray-700 dark:text-gray-300">Columns</span>
        </button>

        <button
          class="px-3 py-2 rounded-md bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 flex items-center text-sm disabled:opacity-50"
          :disabled="selectedProfiles.length === 0"
        >
          <Download :size="16" class="mr-1" />
          <span class="text-gray-700 dark:text-gray-300">Export</span>
        </button>

        <button
          class="px-3 py-2 rounded-md bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-800/40 text-red-700 dark:text-red-400 flex items-center text-sm disabled:opacity-50"
          :disabled="selectedProfiles.length === 0"
        >
          <Trash2 :size="16" class="mr-1" />
          Delete
        </button>

        <button class="px-3 py-2 rounded-md bg-indigo-600 hover:bg-indigo-700 text-white flex items-center text-sm">
          <PlusCircle :size="16" class="mr-1" />
          <span class="text-white">New Profile</span>
        </button>
      </div>
    </div>
    
    <!-- Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <input
                type="checkbox"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700"
                :checked="selectedProfiles.length === allProfiles.length && allProfiles.length > 0"
                @change="toggleSelectAll"
              />
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Name</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Target Website</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Browser Type</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Version</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Proxy</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Created</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Last Used</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Status</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Tags</span>
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <span class="text-gray-500 dark:text-gray-400">Actions</span>
            </th>
          </tr>
        </thead>
        
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr 
            v-for="profile in filteredProfiles" 
            :key="profile.id" 
            class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"
          >
            <td class="px-6 py-4 whitespace-nowrap">
              <input
                type="checkbox"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700"
                :checked="selectedProfiles.includes(profile.id)"
                @change="() => toggleSelectProfile(profile.id)"
              />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ profile.name }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex flex-wrap gap-1">
                <span 
                  v-for="(site, idx) in profile.targetWebsites" 
                  :key="idx" 
                  class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                >
                  {{ site }}
                </span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <component 
                  :is="getBrowserIcon(profile.fingerprintBrowserType)" 
                  :size="18" 
                  :class="{
                    'text-blue-500': profile.fingerprintBrowserType === 'Chrome',
                    'text-teal-500': profile.fingerprintBrowserType === 'Edge',
                    'text-purple-500': profile.fingerprintBrowserType === 'Opera'
                  }"
                />
                <span class="ml-2 text-sm text-gray-900 dark:text-gray-100">{{ profile.fingerprintBrowserType }}</span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ profile.version }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="profile.proxyEnabled 
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' 
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'"
                class="px-2 py-1 text-xs font-medium rounded-full"
              >
                {{ profile.proxyEnabled ? 'Enabled' : 'Disabled' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ formatDate(profile.created) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
              {{ formatDate(profile.lastUsed) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="getStatusBadge(profile.status).classes">
                {{ getStatusBadge(profile.status).text }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center space-x-1">
                <Tag :size="14" class="text-gray-400" />
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ profile.tags.slice(0, 2).join(', ') }}
                  <span v-if="profile.tags.length > 2"> +{{ profile.tags.length - 2 }}</span>
                </span>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                  <Edit :size="16" />
                </button>
                <button class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                  <Play :size="16" />
                </button>
                <button class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">
                  <MoreVertical :size="16" />
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      
      <div v-if="filteredProfiles.length === 0" class="text-center py-10">
        <p class="text-gray-500 dark:text-gray-400">No browser profiles found</p>
      </div>
    </div>

    <!-- Footer with pagination -->
    <div class="px-6 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
      <div class="text-sm text-gray-500 dark:text-gray-400">
        <span class="text-gray-500 dark:text-gray-400">Showing</span> <span class="font-medium text-gray-500 dark:text-gray-400">{{ filteredProfiles.length }}</span> <span class="text-gray-500 dark:text-gray-400">of</span> <span class="font-medium text-gray-500 dark:text-gray-400">{{ allProfiles.length }}</span> <span class="text-gray-500 dark:text-gray-400">profiles</span>
      </div>

      <div class="flex items-center space-x-2">
        <button class="px-2 py-1 rounded bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600">
          <span class="text-gray-700 dark:text-gray-300">Previous</span>
        </button>
        <span class="px-2 py-1 rounded bg-indigo-600 text-white">1</span>
        <button class="px-2 py-1 rounded bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600">
          <span class="text-gray-700 dark:text-gray-300">Next</span>
        </button>

        <select class="ml-2 px-2 py-1 rounded bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600">
          <option value="10" class="text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700">10 / page</option>
          <option value="20" class="text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700">20 / page</option>
          <option value="50" class="text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700">50 / page</option>
        </select>
      </div>
    </div>
  </div>
</template>
