<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { FwbBadge } from 'flowbite-vue'

// Demo data for charts
const cpuUsage = ref(45)
const ramUsage = ref(68)
const diskUsage = ref(32)
const networkSpeed = ref(125)
// Demo data - these would be used for additional features
// const activeProfiles = ref(3)
// const activeSessions = ref(1)

// Chart data
const cpuHistory = ref([30, 35, 42, 38, 45, 48, 45, 42, 39, 45])
const ramHistory = ref([55, 58, 62, 65, 68, 70, 68, 66, 67, 68])
const networkHistory = ref([80, 95, 110, 125, 140, 135, 125, 115, 120, 125])

// Update demo data periodically
onMounted(() => {
  setInterval(() => {
    // Simulate real-time data updates
    cpuUsage.value = Math.max(20, Math.min(80, cpuUsage.value + (Math.random() - 0.5) * 10))
    ramUsage.value = Math.max(40, Math.min(90, ramUsage.value + (Math.random() - 0.5) * 8))
    networkSpeed.value = Math.max(
      50,
      Math.min(200, networkSpeed.value + (Math.random() - 0.5) * 20),
    )

    // Update history arrays
    cpuHistory.value.push(cpuUsage.value)
    cpuHistory.value.shift()

    ramHistory.value.push(ramUsage.value)
    ramHistory.value.shift()

    networkHistory.value.push(networkSpeed.value)
    networkHistory.value.shift()
  }, 3000)
})

const getUsageColor = (usage: number) => {
  if (usage < 50) return 'text-green-600 dark:text-green-400'
  if (usage < 75) return 'text-yellow-600 dark:text-yellow-400'
  return 'text-red-600 dark:text-red-400'
}

const getUsageBarColor = (usage: number) => {
  if (usage < 50) return 'bg-green-500'
  if (usage < 75) return 'bg-yellow-500'
  return 'bg-red-500'
}
</script>

<template>
  <!-- Page Header -->
  <div
    class="p-4 bg-white block sm:flex items-center justify-between border-b border-gray-200 lg:mt-1.5 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="w-full mb-1">
      <div class="mb-4">
        <nav class="flex mb-5" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
            <li class="inline-flex items-center">
              <a
                href="#"
                class="inline-flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-white"
              >
                <svg
                  class="w-5 h-5 mr-2.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                  ></path>
                </svg>
                Home
              </a>
            </li>
            <li>
              <div class="flex items-center">
                <svg
                  class="w-6 h-6 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page"
                  >Dashboard</span
                >
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          System Dashboard
        </h1>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Real-time system monitoring and browser profile activity overview.
        </p>
      </div>
    </div>
  </div>

  <!-- System Metrics Cards -->
  <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-2 2xl:grid-cols-4">
    <!-- CPU Usage -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">CPU Usage</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >{{ cpuUsage.toFixed(1) }}%</span
        >
        <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2 dark:bg-gray-700">
          <div
            :class="getUsageBarColor(cpuUsage)"
            class="h-2.5 rounded-full transition-all duration-300"
            :style="{ width: cpuUsage + '%' }"
          ></div>
        </div>
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400 mt-2">
          <span :class="getUsageColor(cpuUsage)" class="flex items-center mr-1.5 text-sm">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              ></path>
            </svg>
            {{ cpuUsage < 50 ? 'Normal' : cpuUsage < 75 ? 'Moderate' : 'High' }}
          </span>
        </p>
      </div>
    </div>

    <!-- RAM Usage -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">RAM Usage</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >{{ ramUsage.toFixed(1) }}%</span
        >
        <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2 dark:bg-gray-700">
          <div
            :class="getUsageBarColor(ramUsage)"
            class="h-2.5 rounded-full transition-all duration-300"
            :style="{ width: ramUsage + '%' }"
          ></div>
        </div>
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400 mt-2">
          <span :class="getUsageColor(ramUsage)" class="flex items-center mr-1.5 text-sm">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              ></path>
            </svg>
            {{ ramUsage < 50 ? 'Normal' : ramUsage < 75 ? 'Moderate' : 'High' }}
          </span>
        </p>
      </div>
    </div>

    <!-- Disk Usage -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Disk Usage</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >{{ diskUsage }}%</span
        >
        <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2 dark:bg-gray-700">
          <div
            :class="getUsageBarColor(diskUsage)"
            class="h-2.5 rounded-full transition-all duration-300"
            :style="{ width: diskUsage + '%' }"
          ></div>
        </div>
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400 mt-2">
          <span :class="getUsageColor(diskUsage)" class="flex items-center mr-1.5 text-sm">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              ></path>
            </svg>
            {{ diskUsage < 50 ? 'Normal' : diskUsage < 75 ? 'Moderate' : 'High' }}
          </span>
        </p>
      </div>
    </div>

    <!-- Network Speed -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Network Speed</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >{{ networkSpeed.toFixed(0) }} Mbps</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400 mt-2">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            {{ networkSpeed > 100 ? 'Excellent' : networkSpeed > 50 ? 'Good' : 'Poor' }}
          </span>
        </p>
      </div>
    </div>
  </div>

  <!-- Charts Section -->
  <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 mt-6">
    <!-- CPU History Chart -->
    <div
      class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">CPU Usage History</h3>
        <FwbBadge
          type="green"
          size="sm"
          class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
          >Live</FwbBadge
        >
      </div>
      <div class="relative h-64">
        <!-- Simple SVG Chart -->
        <svg class="w-full h-full" viewBox="0 0 400 200">
          <defs>
            <linearGradient id="cpuGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" style="stop-color: #3b82f6; stop-opacity: 0.3" />
              <stop offset="100%" style="stop-color: #3b82f6; stop-opacity: 0" />
            </linearGradient>
          </defs>
          <!-- Grid lines -->
          <g class="stroke-gray-300 dark:stroke-gray-600" stroke-width="1" opacity="0.3">
            <line x1="0" y1="50" x2="400" y2="50" />
            <line x1="0" y1="100" x2="400" y2="100" />
            <line x1="0" y1="150" x2="400" y2="150" />
          </g>
          <!-- Chart line -->
          <polyline
            :points="cpuHistory.map((val, i) => `${i * 40},${200 - val * 2}`).join(' ')"
            fill="url(#cpuGradient)"
            stroke="#3b82f6"
            stroke-width="2"
          />
          <polyline
            :points="cpuHistory.map((val, i) => `${i * 40},${200 - val * 2}`).join(' ')"
            fill="none"
            stroke="#3b82f6"
            stroke-width="2"
          />
        </svg>
      </div>
    </div>

    <!-- RAM History Chart -->
    <div
      class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">RAM Usage History</h3>
        <FwbBadge
          type="green"
          size="sm"
          class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
          >Live</FwbBadge
        >
      </div>
      <div class="relative h-64">
        <svg class="w-full h-full" viewBox="0 0 400 200">
          <defs>
            <linearGradient id="ramGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" style="stop-color: #10b981; stop-opacity: 0.3" />
              <stop offset="100%" style="stop-color: #10b981; stop-opacity: 0" />
            </linearGradient>
          </defs>
          <!-- Grid lines -->
          <g class="stroke-gray-300 dark:stroke-gray-600" stroke-width="1" opacity="0.3">
            <line x1="0" y1="50" x2="400" y2="50" />
            <line x1="0" y1="100" x2="400" y2="100" />
            <line x1="0" y1="150" x2="400" y2="150" />
          </g>
          <!-- Chart line -->
          <polyline
            :points="ramHistory.map((val, i) => `${i * 40},${200 - val * 2}`).join(' ')"
            fill="url(#ramGradient)"
            stroke="#10b981"
            stroke-width="2"
          />
          <polyline
            :points="ramHistory.map((val, i) => `${i * 40},${200 - val * 2}`).join(' ')"
            fill="none"
            stroke="#10b981"
            stroke-width="2"
          />
        </svg>
      </div>
    </div>
  </div>

  <!-- System Information -->
  <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 mt-6">
    <!-- System Status -->
    <div
      class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Status</h3>
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-500 dark:text-gray-400">FM3 Scanner</span>
          <FwbBadge
            type="green"
            size="sm"
            class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
            >Online</FwbBadge
          >
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-500 dark:text-gray-400">Browser Engine</span>
          <FwbBadge
            type="green"
            size="sm"
            class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
            >Ready</FwbBadge
          >
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-500 dark:text-gray-400">Fingerprint Service</span>
          <FwbBadge
            type="green"
            size="sm"
            class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
            >Connected</FwbBadge
          >
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-500 dark:text-gray-400">Proxy Manager</span>
          <FwbBadge
            type="yellow"
            size="sm"
            class="!text-yellow-800 dark:!text-yellow-300 !bg-yellow-100 dark:!bg-yellow-900"
            >Standby</FwbBadge
          >
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-500 dark:text-gray-400">Network Connection</span>
          <FwbBadge
            type="green"
            size="sm"
            class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
            >Stable</FwbBadge
          >
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-500 dark:text-gray-400">Security Status</span>
          <FwbBadge
            type="green"
            size="sm"
            class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
            >Protected</FwbBadge
          >
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div
      class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h3>
      <div class="space-y-3">
        <div class="flex items-start space-x-3">
          <div class="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
          <div>
            <p class="text-sm text-gray-900 dark:text-white">System monitoring started</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Dashboard initialized • 2 min ago
            </p>
          </div>
        </div>
        <div class="flex items-start space-x-3">
          <div class="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
          <div>
            <p class="text-sm text-gray-900 dark:text-white">FM3 directory scanned</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Profile directory checked • 5 min ago
            </p>
          </div>
        </div>
        <div class="flex items-start space-x-3">
          <div class="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
          <div>
            <p class="text-sm text-gray-900 dark:text-white">Application startup</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Ghost Automator Pro started • 10 min ago
            </p>
          </div>
        </div>
        <div class="flex items-start space-x-3">
          <div class="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
          <div>
            <p class="text-sm text-gray-900 dark:text-white">Chromium engine initialized</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              Browser engine ready • 12 min ago
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
