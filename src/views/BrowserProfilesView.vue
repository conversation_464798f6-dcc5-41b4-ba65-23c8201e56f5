<script setup lang="ts">
import EnhancedBrowserProfilesTable from '@/components/browser-profiles/EnhancedBrowserProfilesTable.vue'
</script>

<template>
  <!-- Page Header -->
  <div
    class="p-4 bg-white block sm:flex items-center justify-between border-b border-gray-200 lg:mt-1.5 dark:bg-gray-800 dark:border-gray-700"
  >
    <div class="w-full mb-1">
      <div class="mb-4">
        <nav class="flex mb-5" aria-label="Breadcrumb">
          <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
            <li class="inline-flex items-center">
              <a
                href="#"
                class="inline-flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-white"
              >
                <svg
                  class="w-5 h-5 mr-2.5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"
                  ></path>
                </svg>
                Home
              </a>
            </li>
            <li>
              <div class="flex items-center">
                <svg
                  class="w-6 h-6 text-gray-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page"
                  >Browser Profiles</span
                >
              </div>
            </li>
          </ol>
        </nav>
        <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          Browser Profiles (FM3 Compatible)
        </h1>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          View and launch browser profiles created in FingerPrint Manager 3.0 with superior user
          experience.
        </p>

        <!-- Search Bar - Moved higher for better accessibility -->
        <div class="mt-4 sm:flex">
          <div
            class="items-center mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700"
          >
            <form class="lg:pr-3" action="#" method="GET">
              <label for="profiles-search" class="sr-only">Search</label>
              <div class="relative lg:w-64 xl:w-96">
                <input
                  type="text"
                  name="email"
                  id="profiles-search"
                  class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  placeholder="Search for profiles"
                />
              </div>
            </form>
          </div>
          <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
            <!-- MVP: Profile creation and import features will be added in future phases -->
          </div>
        </div>

        <div
          class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800"
        >
          <p class="text-sm text-blue-800 dark:text-blue-200">
            <strong>MVP:</strong> Reads existing FM3 profiles from
            <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded"
              >C:\Users\<USER>\AppData\Local\BASProfileManager\</code
            >
            and launches them without embedded QT windows.
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Stats Cards -->
  <div class="grid w-full grid-cols-1 gap-4 mt-4 xl:grid-cols-2 2xl:grid-cols-4">
    <!-- Total Profiles -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Total Profiles</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >3</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            Ready to use
          </span>
        </p>
      </div>
    </div>

    <!-- Active Sessions -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Active Sessions</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >1</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-blue-500 dark:text-blue-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            Currently running
          </span>
        </p>
      </div>
    </div>

    <!-- FM3 Profiles -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">FM3 Profiles</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >3</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            Imported successfully
          </span>
        </p>
      </div>
    </div>

    <!-- System Status -->
    <div
      class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800"
    >
      <div class="w-full">
        <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">System Status</h3>
        <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white"
          >Online</span
        >
        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
          <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
            <svg
              class="w-4 h-4"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                clip-rule="evenodd"
                fill-rule="evenodd"
                d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"
              ></path>
            </svg>
            All systems operational
          </span>
        </p>
      </div>
    </div>
  </div>

  <!-- Browser Profiles Table -->
  <div class="flex flex-col mt-6">
    <div class="overflow-x-auto">
      <div class="inline-block min-w-full align-middle">
        <div class="overflow-hidden shadow">
          <FwbTable>
            <FwbTableHead class="bg-gray-50 dark:bg-gray-700">
              <FwbTableHeadCell class="w-4">
                <label for="checkbox-all" class="sr-only">checkbox</label>
                <input
                  id="checkbox-all"
                  type="checkbox"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
              </FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">Name</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">Browser type</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">Engine</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">Proxy</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">Created</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">Lst used</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">Status</FwbTableHeadCell>
              <FwbTableHeadCell class="text-left">Tags</FwbTableHeadCell>
              <FwbTableHeadCell class="text-right">Actions</FwbTableHeadCell>
            </FwbTableHead>
            <FwbTableBody
              class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
            >
              <!-- Shopping Profile -->
              <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                <FwbTableCell class="w-4 p-4">
                  <input
                    id="checkbox-1"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                  Shopping Profile
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                  <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
                    Chrome
                  </div>
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  137
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  Enabled
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  Mar 31, 2024, 03:08 PM
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  May 15, 2024, 05:48 PM
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4">
                  <FwbBadge
                    type="green"
                    size="sm"
                    class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
                    >Active</FwbBadge
                  >
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs"
                    >Shopping personal</span
                  >
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-right">
                  <div class="flex items-center justify-end space-x-2">
                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path d="M10 4a2 2 0 100-4 2 2 0 000 4z" />
                        <path d="M10 20a2 2 0 100-4 2 2 0 000 4z" />
                      </svg>
                    </button>
                  </div>
                </FwbTableCell>
              </FwbTableRow>

              <!-- Social Media Profile -->
              <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                <FwbTableCell class="w-4 p-4">
                  <input
                    id="checkbox-2"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                  Social Media
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                  <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
                    Edge
                  </div>
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  137
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  Enabled
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  Apr 28, 2024, 07:15 PM
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  May 17, 2024, 12:43 PM
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4">
                  <FwbBadge
                    type="green"
                    size="sm"
                    class="!text-green-800 dark:!text-green-300 !bg-green-100 dark:!bg-green-900"
                    >Active</FwbBadge
                  >
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs"
                    >Social work</span
                  >
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-right">
                  <div class="flex items-center justify-end space-x-2">
                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path d="M10 4a2 2 0 100-4 2 2 0 000 4z" />
                        <path d="M10 20a2 2 0 100-4 2 2 0 000 4z" />
                      </svg>
                    </button>
                  </div>
                </FwbTableCell>
              </FwbTableRow>

              <!-- Research Profile -->
              <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                <FwbTableCell class="w-4 p-4">
                  <input
                    id="checkbox-3"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                  Research Profile
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                  <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
                    Chrome
                  </div>
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  137
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  Disabled
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  May 01, 2024, 06:18 PM
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  May 16, 2024, 09:28 PM
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4">
                  <FwbBadge
                    type="default"
                    size="sm"
                    class="!text-gray-800 dark:!text-gray-300 !bg-gray-100 dark:!bg-gray-700"
                    >Inactive</FwbBadge
                  >
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs"
                    >Research work</span
                  >
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-right">
                  <div class="flex items-center justify-end space-x-2">
                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path d="M10 4a2 2 0 100-4 2 2 0 000 4z" />
                        <path d="M10 20a2 2 0 100-4 2 2 0 000 4z" />
                      </svg>
                    </button>
                  </div>
                </FwbTableCell>
              </FwbTableRow>

              <!-- Secure Transactions Profile -->
              <FwbTableRow class="hover:bg-gray-100 dark:hover:bg-gray-700">
                <FwbTableCell class="w-4 p-4">
                  <input
                    id="checkbox-4"
                    type="checkbox"
                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                  Secure Transactions
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                  <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
                    Opera
                  </div>
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  137
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  Enabled
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  May 15, 2024, 11:45 AM
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  May 18, 2024, 07:30 PM
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4">
                  <FwbBadge
                    type="red"
                    size="sm"
                    class="!text-red-800 dark:!text-red-300 !bg-red-100 dark:!bg-red-900"
                    >Idle</FwbBadge
                  >
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                  <span class="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs"
                    >Finance personal</span
                  >
                </FwbTableCell>
                <FwbTableCell class="px-6 py-4 text-right">
                  <div class="flex items-center justify-end space-x-2">
                    <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                        <path d="M10 4a2 2 0 100-4 2 2 0 000 4z" />
                        <path d="M10 20a2 2 0 100-4 2 2 0 000 4z" />
                      </svg>
                    </button>
                  </div>
                </FwbTableCell>
              </FwbTableRow>
            </FwbTableBody>
          </FwbTable>
        </div>
      </div>
    </div>
  </div>
</template>
